import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/project/project_provider.dart';
import '../models/project/project_model.dart';
import '../models/material_provider.dart';
import '../models/material_model.dart' as model;
import '../utils/number_formatter.dart';
import '../themes/app_colors.dart';
import '../themes/app_gradients.dart';
import '../widgets/gradient_card.dart';
import 'project_wizard/project_wizard_screen.dart';
import 'project_detail_screen.dart';
import 'glass_demo_screen.dart';

/// M<PERSON>n hình ch<PERSON>h mới theo thiết kế request10.md
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentPage = 0;
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 0.85);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),

            SizedBox(height: 20),

            // Project List với PageView
            _buildProjectList(),

            SizedBox(height: 20),

            // Page indicator
            _buildPageIndicator(),

            SizedBox(height: 20),

            // Menu options (3 nút)
            _buildMenuOptions(),
          ],
        ),
      ),
    );
  }

  /// Xây dựng header với tiêu đề và menu
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Danh sách công trình',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontSize: 24,
                fontWeight: FontWeight.w300,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.more_vert,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onPressed: () {
              // Xử lý khi nhấn nút menu
            },
          ),
        ],
      ),
    );
  }

  /// Xây dựng danh sách dự án với PageView
  Widget _buildProjectList() {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final projects = projectProvider.projects;

        if (projects.isEmpty) {
          return _buildEmptyProjectState();
        }

        return SizedBox(
          height: 280, // Giảm chiều cao để tránh overflow
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (int page) {
              setState(() {
                _currentPage = page;
              });
            },
            itemCount: projects.length,
            itemBuilder: (context, index) {
              final project = projects[index];

              return AnimatedBuilder(
                animation: _pageController,
                builder: (context, child) {
                  double value = 1.0;
                  double opacity = 1.0;
                  double dx = 0;

                  if (_pageController.position.haveDimensions) {
                    final pageOffset = (_pageController.page ?? 0) - index;
                    value = (1 - (pageOffset.abs() * 0.25)).clamp(0.85, 1.0);
                    opacity = (1 - pageOffset.abs()).clamp(0.5, 1.0);
                    dx = pageOffset * 30;
                  }

                  return Opacity(
                    opacity: opacity,
                    child: Transform(
                      alignment: Alignment.center,
                      transform:
                          Matrix4.identity()
                            ..translate(dx)
                            ..scale(value, value)
                            ..setEntry(3, 2, 0.001)
                            ..rotateY(
                              (_pageController.position.haveDimensions
                                      ? (_pageController.page ?? 0) - index
                                      : 0) *
                                  0.05,
                            ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: GestureDetector(
                          // Nhấn vào bất kỳ đâu để truy cập trang chi tiết
                          onTap: () {
                            projectProvider.selectProject(project);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        ProjectDetailScreen(project: project),
                              ),
                            );
                          },
                          // Nhấn giữ để hiện tùy chọn xóa
                          onLongPress: () {
                            _showProjectOptionsBottomSheet(
                              context,
                              project,
                              projectProvider,
                            );
                          },
                          child: GradientCard(
                            title: project.name,
                            subtitle:
                                '${project.location} • ${project.createdAt.day}/${project.createdAt.month}/${project.createdAt.year}',
                            icon: Icons.home_work,
                            width: double.infinity,
                            height: 80,
                            imagePath: project.imagePath,
                            useColumnLayout: true,
                            padding:
                                EdgeInsets
                                    .zero, // Loại bỏ padding để ảnh full width
                            // Loại bỏ duplicate onTap trong GradientCard
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }

  /// Xây dựng trạng thái trống khi không có dự án
  Widget _buildEmptyProjectState() {
    return Container(
      height: 280, // Đồng bộ với chiều cao PageView
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: GradientCard(
        title: 'Chưa có công trình nào',
        subtitle: 'Nhấn "Thêm mới" để tạo công trình đầu tiên',
        icon: Icons.home_work_outlined,
        width: double.infinity,
        height: 280,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ProjectWizardScreen(),
            ),
          );
        },
      ),
    );
  }

  /// Xây dựng chỉ báo trang với animation
  Widget _buildPageIndicator() {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final projects = projectProvider.projects;

        if (projects.isEmpty) {
          return SizedBox.shrink();
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(projects.length, (index) {
            final isActive = _currentPage == index;
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: isActive ? 12 : 8,
              height: isActive ? 12 : 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    isActive ? AppColors.cardPinkStart : AppColors.textSubtle,
              ),
            );
          }),
        );
      },
    );
  }

  /// Xây dựng 3 nút menu chính
  Widget _buildMenuOptions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppColors.getSurfaceOverlay(context),
            width: 1.5,
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildMenuButton(
                  icon: Icons.library_books,
                  label: 'Thư viện',
                  onTap: () => _showContentBottomSheet(0),
                ),
                _buildMenuButton(
                  icon: Icons.add,
                  label: 'Thêm mới',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ProjectWizardScreen(),
                      ),
                    );
                  },
                ),
                _buildMenuButton(
                  icon: Icons.calculate,
                  label: 'Tính khối lượng',
                  onTap: () => _showContentBottomSheet(2),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildMenuButton(
                  icon: Icons.auto_awesome,
                  label: 'Glass Demo',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const GlassDemoScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Xây dựng nút menu
  Widget _buildMenuButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.transparent,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: AppColors.textPrimary, size: 26),
          ),
          SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w300,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// Hiển thị bottom sheet với nội dung tương ứng
  void _showContentBottomSheet(int index) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true, // Cho phép ẩn bottom sheet khi chạm ra ngoài
      enableDrag: true, // Cho phép kéo để ẩn bottom sheet
      backgroundColor: Colors.transparent,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.6, // Kích thước ban đầu chiếm 60% màn hình
            minChildSize: 0.1, // Kích thước tối thiểu khi vuốt xuống
            maxChildSize: 0.9, // Kích thước tối đa khi vuốt lên
            builder:
                (_, controller) => Container(
                  decoration: BoxDecoration(
                    color: AppColors.getSurfaceOverlay(context),
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                  ),
                  child: ListView(
                    controller: controller,
                    children: [
                      // Thanh kéo (handle bar)
                      Center(
                        child: Container(
                          margin: EdgeInsets.symmetric(vertical: 8),
                          width: 40,
                          height: 4,
                          decoration: BoxDecoration(
                            color: AppColors.textSubtle,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                      // Nội dung dựa trên index
                      _buildSelectedContent(index),
                    ],
                  ),
                ),
          ),
    );
  }

  /// Xây dựng nội dung dựa trên menu được chọn
  Widget _buildSelectedContent(int index) {
    switch (index) {
      case 0:
        return _buildMaterialLibraryContent();
      case 1:
        return _buildAddNewContent();
      case 2:
        return _buildCalculateVolumeContent();
      default:
        return _buildMaterialLibraryContent();
    }
  }

  /// Xây dựng nội dung thư viện vật liệu
  Widget _buildMaterialLibraryContent() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.getSurfaceOverlay(context),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Thư viện vật liệu',
                  style: TextStyle(
                    color: AppColors.textPrimary,
                    fontSize: 24,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(Icons.download, color: AppColors.textPrimary),
                    onPressed: () {
                      // Xử lý download
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.upload, color: AppColors.textPrimary),
                    onPressed: () {
                      // Xử lý upload
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.add, color: AppColors.textPrimary),
                    tooltip: 'Thêm vật liệu mới',
                    onPressed: () {
                      _showAddMaterialDialog(context);
                    },
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: 16),

          Consumer<MaterialProvider>(
            builder: (context, materialProvider, child) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Materials title
                    Text(
                      'Vật liệu',
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 20,
                        fontWeight: FontWeight.w400,
                      ),
                    ),

                    SizedBox(height: 8),

                    // Materials list
                    ...materialProvider.materials
                        .where(
                          (material) =>
                              material.name != 'Nhân công xây dựng' &&
                              material.name != 'Nhân công điện nước',
                        )
                        .map((material) {
                          return _buildMaterialItem(
                            name: material.name,
                            unit: material.unit,
                            price: material.pricePerUnit,
                            color: _getMaterialColor(material.name),
                          );
                        }),

                    SizedBox(height: 16),

                    // Labor title
                    Text(
                      'Nhân công',
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    SizedBox(height: 8),

                    // Labor list
                    ...materialProvider.materials
                        .where(
                          (material) =>
                              material.name == 'Nhân công xây dựng' ||
                              material.name == 'Nhân công điện nước',
                        )
                        .map((material) {
                          return _buildMaterialItem(
                            name: material.name,
                            unit: material.unit,
                            price: material.pricePerUnit,
                            color: Colors.pink,
                          );
                        }),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Xây dựng item vật liệu
  Widget _buildMaterialItem({
    required String name,
    required String unit,
    required double price,
    required Color color,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: AppGradients.cardGradient,
      ),
      child: Container(
        margin: EdgeInsets.all(1.5),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.5),
          color: AppColors.getSurfaceOverlay(context),
        ),
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(color: color, shape: BoxShape.circle),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Text(
                    unit,
                    style: TextStyle(color: AppColors.textSubtle, fontSize: 12),
                  ),
                ],
              ),
            ),
            Text(
              '${NumberFormatter.formatCurrency(price)} VNĐ',
              style: TextStyle(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Lấy màu cho vật liệu từ AppColors
  Color _getMaterialColor(String materialName) {
    return AppColors.getMaterialColor(materialName);
  }

  /// Xây dựng nội dung thêm mới
  Widget _buildAddNewContent() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.getSurfaceOverlay(context),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      padding: EdgeInsets.all(16),
      child: Center(
        child: Text(
          'Chức năng thêm mới sẽ được triển khai',
          style: TextStyle(color: AppColors.textPrimary, fontSize: 18),
        ),
      ),
    );
  }

  /// Xây dựng nội dung tính khối lượng
  Widget _buildCalculateVolumeContent() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.getSurfaceOverlay(context),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      padding: EdgeInsets.all(16),
      child: Center(
        child: Text(
          'Chức năng tính khối lượng sẽ được triển khai',
          style: TextStyle(color: AppColors.textPrimary, fontSize: 18),
        ),
      ),
    );
  }

  /// Hiển thị dialog thêm vật liệu mới (từ material_library_screen.dart)
  void _showAddMaterialDialog(BuildContext context) {
    final nameController = TextEditingController();
    final priceController = TextEditingController();
    final unitController = TextEditingController();
    final lengthController = TextEditingController(text: '0.2');
    final widthController = TextEditingController(text: '0.1');
    final heightController = TextEditingController(text: '0.05');
    var selectedUnit = model.MeasurementUnit.piece;
    var showDimensions = selectedUnit == model.MeasurementUnit.piece;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.add_circle, color: Color(0xFF6A3DE8)),
                    SizedBox(width: 8),
                    Text('Thêm vật liệu mới'),
                  ],
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextField(
                        controller: nameController,
                        decoration: InputDecoration(
                          labelText: 'Tên vật liệu',
                          prefixIcon: Icon(Icons.inventory),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: priceController,
                        decoration: InputDecoration(
                          labelText: 'Giá',
                          prefixText: 'VNĐ ',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<model.MeasurementUnit>(
                        value: selectedUnit,
                        decoration: InputDecoration(
                          labelText: 'Đơn vị đo lường',
                          prefixIcon: Icon(Icons.straighten),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        items:
                            model.MeasurementUnit.values.map((unit) {
                              String label;
                              switch (unit) {
                                case model.MeasurementUnit.piece:
                                  label = 'Viên';
                                  break;
                                case model.MeasurementUnit.cubicMeter:
                                  label = 'Mét khối (m³)';
                                  break;
                                case model.MeasurementUnit.squareMeter:
                                  label = 'Mét vuông (m²)';
                                  break;
                                case model.MeasurementUnit.meter:
                                  label = 'Mét (m)';
                                  break;
                                case model.MeasurementUnit.kilogram:
                                  label = 'Kilogram (kg)';
                                  break;
                                case model.MeasurementUnit.ton:
                                  label = 'Tấn';
                                  break;
                                case model.MeasurementUnit.set:
                                  label = 'Bộ';
                                  break;
                                case model.MeasurementUnit.package:
                                  label = 'Gói';
                                  break;
                                case model.MeasurementUnit.custom:
                                  label = 'Tùy chỉnh';
                                  break;
                              }
                              return DropdownMenuItem(
                                value: unit,
                                child: Text(label),
                              );
                            }).toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedUnit = value!;
                            showDimensions =
                                selectedUnit == model.MeasurementUnit.piece;
                          });
                        },
                      ),
                      if (selectedUnit == model.MeasurementUnit.custom) ...[
                        const SizedBox(height: 16),
                        TextField(
                          controller: unitController,
                          decoration: InputDecoration(
                            labelText: 'Đơn vị tùy chỉnh',
                            prefixIcon: Icon(Icons.edit),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ],
                      if (showDimensions) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Kích thước viên (mét)',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF6A3DE8),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: lengthController,
                                decoration: InputDecoration(
                                  labelText: 'Dài (m)',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextField(
                                controller: widthController,
                                decoration: InputDecoration(
                                  labelText: 'Rộng (m)',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: TextField(
                                controller: heightController,
                                decoration: InputDecoration(
                                  labelText: 'Cao (m)',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Hủy',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFF6A3DE8), Color(0xFF4A3DE8)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextButton(
                      onPressed: () {
                        if (nameController.text.isEmpty ||
                            priceController.text.isEmpty ||
                            (selectedUnit == model.MeasurementUnit.custom &&
                                unitController.text.isEmpty)) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Vui lòng điền đầy đủ thông tin'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        final price =
                            double.tryParse(priceController.text) ?? 0.0;
                        if (price <= 0) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Giá phải lớn hơn 0'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }

                        // Lấy kích thước nếu là đơn vị viên
                        double? pieceLength, pieceWidth, pieceHeight;
                        if (showDimensions) {
                          pieceLength = double.tryParse(lengthController.text);
                          pieceWidth = double.tryParse(widthController.text);
                          pieceHeight = double.tryParse(heightController.text);

                          if (pieceLength == null ||
                              pieceLength <= 0 ||
                              pieceWidth == null ||
                              pieceWidth <= 0 ||
                              pieceHeight == null ||
                              pieceHeight <= 0) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Kích thước viên không hợp lệ'),
                                backgroundColor: Colors.red,
                              ),
                            );
                            return;
                          }
                        }

                        Provider.of<MaterialProvider>(
                          context,
                          listen: false,
                        ).addCustomMaterial(
                          name: nameController.text,
                          pricePerUnit: price,
                          customUnit:
                              selectedUnit == model.MeasurementUnit.custom
                                  ? unitController.text
                                  : _getUnitString(selectedUnit),
                          measurementUnit: selectedUnit,
                          pieceLength: pieceLength,
                          pieceWidth: pieceWidth,
                          pieceHeight: pieceHeight,
                        );

                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Đã thêm vật liệu "${nameController.text}"',
                            ),
                            backgroundColor: Color(0xFF6A3DE8),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      child: Text('Thêm'),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }

  /// Lấy chuỗi đơn vị từ MeasurementUnit
  String _getUnitString(model.MeasurementUnit unit) {
    switch (unit) {
      case model.MeasurementUnit.piece:
        return 'viên';
      case model.MeasurementUnit.cubicMeter:
        return 'm³';
      case model.MeasurementUnit.squareMeter:
        return 'm²';
      case model.MeasurementUnit.meter:
        return 'm';
      case model.MeasurementUnit.kilogram:
        return 'kg';
      case model.MeasurementUnit.ton:
        return 'tấn';
      case model.MeasurementUnit.set:
        return 'bộ';
      case model.MeasurementUnit.package:
        return 'gói';
      case model.MeasurementUnit.custom:
        return 'tùy chỉnh';
    }
  }

  /// Hiển thị dialog chỉnh sửa tên và địa điểm dự án
  void _showEditProjectDialog(
    BuildContext context,
    Project project,
    ProjectProvider projectProvider,
  ) {
    final nameController = TextEditingController(text: project.name);
    final locationController = TextEditingController(text: project.location);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: AppColors.getSurfaceOverlay(context),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom + 16,
              left: 16,
              right: 16,
              top: 24,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.textSubtle,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),

                // Title
                Row(
                  children: [
                    Icon(Icons.edit, color: AppColors.cardPinkStart),
                    const SizedBox(width: 8),
                    Text(
                      'Chỉnh sửa dự án',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Name field
                TextField(
                  controller: nameController,
                  style: TextStyle(color: AppColors.textPrimary),
                  decoration: InputDecoration(
                    labelText: 'Tên dự án',
                    labelStyle: TextStyle(color: AppColors.textSubtle),
                    prefixIcon: Icon(
                      Icons.home_work,
                      color: AppColors.cardPinkStart,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.textSubtle),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.textSubtle),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: AppColors.cardPinkStart,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: AppColors.getSurfaceOverlay(context),
                  ),
                ),
                const SizedBox(height: 16),

                // Location field
                TextField(
                  controller: locationController,
                  style: TextStyle(color: AppColors.textPrimary),
                  decoration: InputDecoration(
                    labelText: 'Địa điểm',
                    labelStyle: TextStyle(color: AppColors.textSubtle),
                    prefixIcon: Icon(
                      Icons.location_on,
                      color: AppColors.cardPinkStart,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.textSubtle),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.textSubtle),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: AppColors.cardPinkStart,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: AppColors.getSurfaceOverlay(context),
                  ),
                ),
                const SizedBox(height: 24),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        'Hủy',
                        style: TextStyle(color: AppColors.textSubtle),
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        gradient: AppGradients.cardGradient,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextButton(
                        onPressed: () {
                          if (nameController.text.isNotEmpty &&
                              locationController.text.isNotEmpty) {
                            _updateProject(
                              context,
                              project,
                              projectProvider,
                              nameController.text,
                              locationController.text,
                            );
                            Navigator.pop(context);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Vui lòng điền đầy đủ thông tin'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: AppColors.textPrimary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                        ),
                        child: const Text('Lưu'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Cập nhật thông tin dự án
  void _updateProject(
    BuildContext context,
    Project project,
    ProjectProvider projectProvider,
    String newName,
    String newLocation,
  ) {
    try {
      final updatedProject = Project(
        id: project.id,
        name: newName,
        location: newLocation,
        imagePath: project.imagePath,
        createdAt: project.createdAt,
        updatedAt: DateTime.now(),
        floors: project.floors,
        roof: project.roof,
        foundationType: project.foundationType,
        structureType: project.structureType,
        selectedMaterialIds: project.selectedMaterialIds,
        detailedParameters: project.detailedParameters,
        results: project.results,
      );

      projectProvider.updateProject(updatedProject);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đã cập nhật dự án "$newName"'),
          backgroundColor: Color(0xFF6A3DE8),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi khi cập nhật dự án: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Hiển thị bottom sheet với tùy chọn dự án
  void _showProjectOptionsBottomSheet(
    BuildContext context,
    Project project,
    ProjectProvider projectProvider,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: BoxDecoration(
              color: AppColors.getSurfaceOverlay(context),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: Offset(0, -5),
                ),
              ],
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    margin: EdgeInsets.only(top: 8, bottom: 16),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Project info header
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        Icon(Icons.home_work, color: Color(0xFF6A3DE8)),
                        SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                project.name,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.white70,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                project.location,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w300,
                                  color: Colors.white70,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  Divider(height: 1),

                  // Options
                  ListTile(
                    leading: Icon(Icons.edit, color: Color(0xFF6A3DE8)),
                    title: Text('Chỉnh sửa tên và địa điểm'),
                    onTap: () {
                      Navigator.pop(context);
                      _showEditProjectDialog(context, project, projectProvider);
                    },
                  ),

                  ListTile(
                    leading: Icon(Icons.copy, color: Colors.blue),
                    title: Text('Sao chép dự án'),
                    onTap: () {
                      Navigator.pop(context);
                      projectProvider.duplicateProject(project);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Đã sao chép dự án "${project.name}"'),
                          backgroundColor: Color(0xFF6A3DE8),
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      );
                    },
                  ),

                  Divider(height: 1),

                  ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text(
                      'Xóa dự án',
                      style: TextStyle(color: Colors.red),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _showDeleteConfirmDialog(
                        context,
                        project,
                        projectProvider,
                      );
                    },
                  ),

                  SizedBox(height: 16),
                ],
              ),
            ),
          ),
    );
  }

  /// Hiển thị dialog xác nhận xóa dự án
  void _showDeleteConfirmDialog(
    BuildContext context,
    Project project,
    ProjectProvider projectProvider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.warning, color: Colors.red),
                SizedBox(width: 8),
                Text('Xác nhận xóa'),
              ],
            ),
            content: Text(
              'Bạn có chắc chắn muốn xóa dự án "${project.name}"?\n\nHành động này không thể hoàn tác.',
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Hủy', style: TextStyle(color: Colors.grey[600])),
              ),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.red.shade400, Colors.red.shade700],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    projectProvider.deleteProject(project.id);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Đã xóa dự án "${project.name}"'),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    );
                  },
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: Text('Xóa'),
                ),
              ),
            ],
          ),
    );
  }
}
